import {useMemo} from 'react';
import {useRouter} from 'next/router';
import {ColumnDef} from '@tanstack/react-table';
import {useTrans} from '@core/hooks';
import {Transaction} from './types';

const useColumns = () => {
    const router = useRouter();

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    const t = useTrans();

    const columns = useMemo<ColumnDef<Transaction>[]>(
        () => [
            {
                header: t('Issue Date'),
                accessorKey: 'issueDate',
                cell: ({row}) => {
                    const issueDate = row.original.issueDate;

                    return (
                        <span className="whitespace-nowrap">
                            {new Date(issueDate).toLocaleDateString(
                                router.locale
                            )}
                        </span>
                    );
                }
            },
            {
                header: t('Due Date'),
                accessorKey: 'dueDate',
                cell: ({row}) => {
                    const dueDate = row.original.dueDate;
                    console.log('row', row.original);

                    return (
                        <span className="whitespace-nowrap">
                            {new Date(dueDate).toLocaleDateString(
                                router.locale
                            )}
                        </span>
                    );
                }
            },
            {
                header: t('Description'),
                accessorKey: 'description'
            },
            {
                header: t('Reference'),
                accessorKey: 'reference'
            },
            {
                header: t('Project Code'),
                accessorKey: 'projectCode'
            },
            {
                header: t('Project Name'),
                accessorKey: 'projectName'
            },
            {
                header: t('Debit'),
                accessorKey: 'debit',
                cell: ({row}) => (
                    <p className="whitespace-nowrap text-right font-medium text-green-700">
                        {priceFormatter.format(row.original.debit)} TL
                    </p>
                )
            },
            {
                header: t('Credit'),
                accessorKey: 'credit',
                cell: ({row}) => (
                    <p className="whitespace-nowrap text-right font-medium text-red-700">
                        {priceFormatter.format(row.original.credit)} TL
                    </p>
                )
            },
            {
                header: t('Balance'),
                accessorKey: 'balance',
                cell: ({row}) => (
                    <p className="whitespace-nowrap text-right font-medium">
                        {priceFormatter.format(row.original.balance)} TL
                    </p>
                )
            }
        ],
        // eslint-disable-next-line
        []
    );

    return columns;
};

export default useColumns;
