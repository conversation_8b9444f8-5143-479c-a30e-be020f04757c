import {useMemo, useState, useRef, useEffect} from 'react';
import {useRouter} from 'next/router';
import CurrencyInput from 'react-currency-input-field';
import {cls, debounce} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiRadioGroup} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';
import usePayment from './context';
import CurrencySelector from './CurrencySelector';

type PaymentPlan = 'partial' | 'total' | null;

const BalanceGroup = () => {
    const t = useTrans();
    const router = useRouter();

    const [paymentPlan, setPaymentPlan] = useState<PaymentPlan>(null);
    const [currencyAmount, setCurrencyAmount] = useState<string>('');
    const [tlAmount, setTlAmount] = useState<string>('');
    const isUpdatingFromCurrency = useRef(false);
    const isUpdatingFromTL = useRef(false);

    const {
        balance,
        isLoading,
        setSelectedBalance,
        selectedCurrency,
        convertAmount
    } = usePayment();

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    // Para birimi inputu değiştiğinde
    const debouncedCurrencyChangeHandler = debounce(
        (value: string | undefined) => {
            if (isUpdatingFromTL.current) return;

            isUpdatingFromCurrency.current = true;

            if (value === undefined || value === '') {
                setSelectedBalance(0);
                setTlAmount('');
            } else {
                const enteredAmount = parseFloat(value.replace(',', '.'));
                // Girilen tutarı seçilen para biriminden TL'ye çevir
                const tlAmountConverted = convertAmount(
                    enteredAmount,
                    selectedCurrency.code,
                    'TL'
                );
                console.log(
                    `Converting ${enteredAmount} ${selectedCurrency.code} to TL: ${tlAmountConverted}`
                );
                setSelectedBalance(tlAmountConverted);
                setTlAmount(tlAmountConverted.toFixed(2));
            }

            setTimeout(() => {
                isUpdatingFromCurrency.current = false;
            }, 100);
        },
        300
    );

    // TL inputu değiştiğinde
    const debouncedTLChangeHandler = debounce((value: string | undefined) => {
        if (isUpdatingFromCurrency.current) return;

        isUpdatingFromTL.current = true;

        if (value === undefined || value === '') {
            setSelectedBalance(0);
            setCurrencyAmount('');
        } else {
            const enteredTLAmount = parseFloat(value.replace(',', '.'));
            // TL tutarını seçilen para birimine çevir
            const currencyAmountConverted = convertAmount(
                enteredTLAmount,
                'TL',
                selectedCurrency.code
            );
            console.log(
                `Converting ${enteredTLAmount} TL to ${selectedCurrency.code}: ${currencyAmountConverted}`
            );
            setSelectedBalance(enteredTLAmount);
            setCurrencyAmount(currencyAmountConverted.toFixed(2));
        }

        setTimeout(() => {
            isUpdatingFromTL.current = false;
        }, 100);
    }, 300);

    // Seçilen para birimindeki bakiye
    const displayBalance = useMemo(() => {
        return convertAmount(balance, 'TL', selectedCurrency.code);
    }, [balance, selectedCurrency.code, convertAmount]);

    // Para birimi değiştiğinde input'ları temizle
    useEffect(() => {
        setCurrencyAmount('');
        setTlAmount('');
        setSelectedBalance(0);
    }, [selectedCurrency.code, setSelectedBalance]);

    return (
        <div className="mb-6">
            <CurrencySelector />

            <UiRadioGroup value={paymentPlan} onChange={setPaymentPlan}>
                <div className="grid gap-4 lg:grid-cols-2">
                    <UiRadioGroup.Option
                        value="total"
                        onClick={() => setSelectedBalance(balance)}
                        disabled={balance <= 0 || isLoading}
                        className={({checked}) =>
                            cls(
                                checked && 'border-transparent',
                                'shadow-small relative w-full cursor-pointer rounded-lg border bg-white p-5 focus:outline-none disabled:cursor-not-allowed disabled:opacity-40'
                            )
                        }
                    >
                        {({checked}) => (
                            <div className="flex items-center">
                                {checked ? (
                                    <CheckCircleIcon className="mr-4 h-7 w-7 text-primary-600" />
                                ) : (
                                    <div className="mr-4 h-7 w-7 rounded-full border"></div>
                                )}

                                <div className="flex flex-1">
                                    <div className="flex flex-1 flex-col">
                                        <UiRadioGroup.Label
                                            as="span"
                                            className="block text-sm font-medium"
                                        >
                                            {t('Balance')}
                                        </UiRadioGroup.Label>

                                        <UiRadioGroup.Description
                                            as="span"
                                            className="mt-1 flex items-center text-sm text-muted"
                                        >
                                            {t('Pay all the balance')}
                                        </UiRadioGroup.Description>
                                    </div>

                                    <div className="ml-4 self-center text-sm font-medium">
                                        <UiRadioGroup.Description>
                                            {isLoading ? (
                                                <span className="skeleton-card block h-6 w-24"></span>
                                            ) : (
                                                <span className="font-medium text-primary-600">
                                                    {priceFormatter.format(
                                                        displayBalance
                                                    )}{' '}
                                                    {selectedCurrency.symbol}
                                                </span>
                                            )}
                                        </UiRadioGroup.Description>
                                    </div>
                                </div>

                                <div
                                    className={cls(
                                        checked
                                            ? 'border-primary-600'
                                            : 'border-transparent',
                                        'pointer-events-none absolute -inset-px rounded-md border-2'
                                    )}
                                />
                            </div>
                        )}
                    </UiRadioGroup.Option>

                    <UiRadioGroup.Option
                        value="partial"
                        onClick={() => {
                            if (paymentPlan === 'total') setSelectedBalance(0);
                        }}
                        disabled={isLoading}
                        className={({checked}) =>
                            cls(
                                checked && 'border-transparent',
                                'shadow-small relative w-full cursor-pointer rounded-lg border bg-white p-5 focus:outline-none disabled:cursor-not-allowed disabled:opacity-40'
                            )
                        }
                    >
                        {({checked}) => (
                            <div className="flex items-center">
                                {checked ? (
                                    <CheckCircleIcon className="mr-4 h-7 w-7 text-primary-600" />
                                ) : (
                                    <div className="mr-4 h-7 w-7 rounded-full border"></div>
                                )}

                                <div className="flex flex-1 flex-col max-md:gap-2 md:flex-row">
                                    <div className="flex flex-1 flex-col">
                                        <UiRadioGroup.Label
                                            as="span"
                                            className="block text-sm font-medium"
                                        >
                                            {t('Pay Amount')}
                                        </UiRadioGroup.Label>

                                        <UiRadioGroup.Description
                                            as="span"
                                            className="mt-1 flex items-center text-sm text-muted"
                                        >
                                            {t(
                                                'Enter how much you are willing to pay'
                                            )}
                                        </UiRadioGroup.Description>
                                    </div>

                                    {checked && (
                                        <div className="text-sm font-medium md:ml-4 md:self-center">
                                            <UiRadioGroup.Description
                                                as="div"
                                                className="space-y-2"
                                            >
                                                {/* Para birimi inputu */}
                                                <div>
                                                    <label className="mb-1 block text-xs text-gray-600">
                                                        {t('Amount')} (
                                                        {selectedCurrency.code})
                                                    </label>
                                                    <CurrencyInput
                                                        value={currencyAmount}
                                                        placeholder={`${priceFormatter.format(
                                                            displayBalance
                                                        )} ${
                                                            selectedCurrency.symbol
                                                        }`}
                                                        decimalsLimit={2}
                                                        decimalScale={2}
                                                        allowNegativeValue={
                                                            false
                                                        }
                                                        disableAbbreviations
                                                        allowDecimals
                                                        suffix={` ${selectedCurrency.symbol}`}
                                                        className="w-40 rounded-lg !border-secondary-300 text-sm focus:border-primary-600 focus:ring-2 focus:!ring-primary-600"
                                                        onValueChange={value => {
                                                            setCurrencyAmount(
                                                                value || ''
                                                            );
                                                            debouncedCurrencyChangeHandler(
                                                                value
                                                            );
                                                        }}
                                                    />
                                                </div>

                                                {/* TL inputu */}
                                                <div>
                                                    <label className="mb-1 block text-xs text-gray-600">
                                                        {t('Amount')} (TL) - SBP
                                                    </label>
                                                    <CurrencyInput
                                                        value={tlAmount}
                                                        placeholder="0.00 TL"
                                                        decimalsLimit={2}
                                                        decimalScale={2}
                                                        allowNegativeValue={
                                                            false
                                                        }
                                                        disableAbbreviations
                                                        allowDecimals
                                                        suffix=" TL"
                                                        className="w-40 rounded-lg !border-secondary-300 text-sm focus:border-primary-600 focus:ring-2 focus:!ring-primary-600"
                                                        onValueChange={value => {
                                                            setTlAmount(
                                                                value || ''
                                                            );
                                                            debouncedTLChangeHandler(
                                                                value
                                                            );
                                                        }}
                                                    />
                                                </div>
                                            </UiRadioGroup.Description>
                                        </div>
                                    )}
                                </div>

                                <div
                                    className={cls(
                                        checked
                                            ? 'border-primary-600'
                                            : 'border-transparent',
                                        'pointer-events-none absolute -inset-px rounded-md border-2'
                                    )}
                                />
                            </div>
                        )}
                    </UiRadioGroup.Option>
                </div>
            </UiRadioGroup>
        </div>
    );
};

export default BalanceGroup;
